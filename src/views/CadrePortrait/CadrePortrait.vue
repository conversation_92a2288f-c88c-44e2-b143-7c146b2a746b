<template>
	<Layout title="干部画像">
		<div class="cadre-portrait">
			<!-- <div class="menu-box">
				<div class="menu-list">
					<div
						:class="[`menu-item menu-item-${item.key}`, currentKey === item.key && 'menu-active']"
						v-for="item in menu"
						:key="item.key"
						@click="onMenuClick(item)"
					>
						{{ item.label }}
					</div>
				</div>
			</div> -->
			<div class="menu-box">
				<div
					class="menu-list"
					:style="{
						justifyContent: LIMIT_VERSION || userType == '1' ? 'flex-start' : '',
						gap: LIMIT_VERSION || userType == '1' ? '8%' : '10px',
					}"
				>
					<div
						:class="[`menu-item menu-item-${item.key}`, currentKey === item.key && 'menu-active']"
						v-for="item in menu"
						:key="item.key"
						@click="onMenuClick(item)"
					>
						{{ item.label }}
					</div>
				</div>
			</div>

			<div class="content-box">
				<router-view v-slot="{ Component }">
					<keep-alive include="SearchComparison">
						<component :is="Component" />
					</keep-alive>
				</router-view>
			</div>
			<transition name="container" appear>
				<div class="container" v-if="!!componentName">
					<component :is="componentName" :user="userInfo" @close="onComponentClose" />
				</div>
			</transition>
			<FixedGroup>
				<FixedMenu :menu="starMenu" v-if="!LIMIT_VERSION" />
				<FixedMenu :menu="controllMenu" size="small" :active-key="menuKey" />
				<FixedMenu :menu="backMenu" />
			</FixedGroup>
			<RuleModal :visible="ruleModalVisible" @close="onRuleModal" :url="ruleImage" :title="ruleTitle"></RuleModal>
			<a-modal class="folder-modal" :closable="false" :visible="folderVisible" width="" destroyOnClose :footer="null" @cancel="onClose">
				<Folder @close="onClose" @success="onSuccess" :source-id="(user_id as any)" modal-type="collect" />
			</a-modal>
		</div>
	</Layout>
</template>

<script lang="ts" setup>
import { getLeaderBaseInfo } from '@/apis/cadre-portrait/home'
import Layout from '@/layout/index.vue'
import { UserInfo } from '@/types/user'
import type { Component } from 'vue'
import { provide, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import { getUserType } from '@/apis/cadreSystem'
import quguanImage from '@/assets/images/quguan.png'
import zhongcengImage from '@/assets/images/zhongceng.png'
import FixedGroup from '@/components/FixedGroup.vue'
import FixedMenu from '@/components/FixedMenu.vue'
import Folder from '@/components/Folder.vue'
import Dimension from './components/dimension.vue'
import Investigate from './components/investigate.vue'
import Personal from './components/personal.vue'

import back1 from '@/assets/images/back-1.png'
import dangerImage from '@/assets/images/danger-image.png'
import _ruleImage from '@/assets/images/rule-image.png'
import starPng from '@/assets/images/star.png'

import { LIMIT_VERSION } from '@/config/env'
import useKeepAlive from '@/store/keepalive'
import useUser from '@/store/user'
import { appendParamsToUrl } from '@/utils/utils'

type MenuType = {
	label: string
	key: number
}

const _menu = [
	{
		label: '干部画像',
		key: 1,
		path: '/cadre-portrait/home',
	},
	{
		label: '任免审批表',
		key: 2,
		// path: '/cadre-portrait/cadre-table',
		path: '/cadre-portrait/cadre-table',
	},
	{
		label: '关系图谱',
		key: 3,
		path: '/cadre-portrait/relationship-graph',
	},
	// {
	// 	label: '查询对比',
	// 	key: 4,
	// 	// path: '/cadre-portrait/search-comparison',
	// 	path: '/cadre-portrait/search-comparison',
	// },
]

const user = useUser()
const route = useRoute()
const router = useRouter()
const AUTH_LIST = ['/cadre-portrait/home', '/cadre-portrait/relationship-graph']

const keepalive = useKeepAlive()
const { user_id } = route.query
const ruleImage = ref('')
const ruleTitle = ref('')
const componentName = ref<Component>()
const folderVisible = ref(false)
const currentKey: any = ref(1)
const ruleModalVisible = ref(false)
const menuKey = ref()

const userInfo = ref<UserInfo | any>({
	user_id: 0,
	name: '',
	birthday_age: '',
	education: '',
	current_position: '',
	position_time: '',
	leader_index: 0,
	leader_index_rank: '',
	risk_index: 0,
	risk_index_avg: 0,
	feature: [],
	goods_range: '',
	like: '',
	sketch: '',
	areas_responsibility: [],
	feature_list: [],
	sketch_list: [],
})
// const riskIndex = computed(() => {
// 	return {
// 		rank: riskList[0],
// 		total: riskList[1],
// 	}
// })
keepalive.push('CadrePortrait')

const menu = ref<Array<MenuType>>(_menu)

if (LIMIT_VERSION) {
	menu.value.pop()
}
const userType = ref()
// 根据 userType 动态过滤菜单
watch(
	userType,
	(newVal) => {
		if (userType.value == '1') {
			menu.value = [
				{
					label: '任免审批表',
					key: 2,
					// path: '/cadre-portrait/cadre-table',
					path: '/cadre-portrait/cadre-table',
				},
				// {
				// 	label: '查询对比',
				// 	key: 4,
				// 	// path: '/cadre-portrait/search-comparison',
				// 	path: '/cadre-portrait/search-comparison',
				// },
			]
		} else {
			menu.value = _menu
		}
	},
	{
		immediate: true,
	}
)
// 根据 userType 动态过滤菜单
watch(
	userType,
	() => {
		if (userType.value == '1') {
			// 如果 userType 是 1，移除现实表现和日常监督
			controllMenu.value = controllMenu.value.filter((item) => {
				return item.key !== '2' && item.key !== '3'
			})
		} else {
			// 如果不是 2，恢复所有菜单
			controllMenu.value = [
				{
					label: '干部标注',
					key: '1',
					click: () => {
						componentName.value = Dimension
						menuKey.value = '1'
					},
				},
				{
					label: '现实表现',
					key: '2',
					click: () => {
						componentName.value = Investigate
						menuKey.value = '2'
					},
				},
				{
					label: '日常监督',
					key: '3',
					click: () => {
						componentName.value = Personal
						menuKey.value = '3'
					},
				},
			]
		}
	},
	{ immediate: true }
)
// const user_id = 283157451515
/**
 * @description: 获取干部基本信息
 * @return {*}
 */
const initLeaderBaseInfoData = async () => {
	const res = await getLeaderBaseInfo({ user_id })
	userInfo.value = res.data
	user.updateDetail(res.data)
}
/**
 * @description:  菜单点击事件
 * @param {*} key
 * @return {*}
 */
const onMenuClick = ({ path, url }: any) => {
	if (url) {
		window.open(appendParamsToUrl(url, { user_id }))
	} else {
		router.replace({
			path,
			query: {
				user_id,
				_h: route.query._h,
			},
		})
	}
}
// 初始化数据

const onRuleModal = (type?: number) => {
	if (type === 1) {
		ruleImage.value = userType.value == '2' ? quguanImage : zhongcengImage
		ruleTitle.value = '干部指数'
	} else if (type === 2) {
		ruleImage.value = dangerImage
		ruleTitle.value = '风险指数'
	} else if (type === 3) {
		ruleImage.value = _ruleImage
		ruleTitle.value = '计分细则'
	}

	ruleModalVisible.value = !ruleModalVisible.value

	if (type === undefined) {
		setTimeout(() => {
			ruleImage.value = ''
		}, 200)
	}
}

const starMenu = [
	{
		label: '收藏',
		icon: starPng,
		click: () => {
			folderVisible.value = true

			menuKey.value = undefined
			componentName.value = undefined
		},
	},
]
const controllMenu = ref([
	{
		label: '干部标注',
		key: '1',
		click: () => {
			componentName.value = Dimension
			menuKey.value = '1'
		},
	},
	{
		label: '现实表现',
		key: '2',
		click: () => {
			componentName.value = Investigate
			menuKey.value = '2'
		},
	},
	{
		label: '日常监督',
		key: '3',
		click: () => {
			componentName.value = Personal
			menuKey.value = '3'
		},
	},
	// {
	// 	label: '廉政信访',
	// 	click: () => {},
	// },
	// {
	// 	label: '班子回访',
	// 	click: () => {},
	// },
	// {
	// 	label: '分管经验',
	// 	click: () => {},
	// },
	// {
	// 	label: '个人事项',
	// 	key: '4',
	// 	click: () => {
	// 		componentName.value = Matters
	// 		menuKey.value = '4'
	// 	},
	// },
	// {
	// 	label: '培训信息',
	// 	click: () => {},
	// },
])
if (LIMIT_VERSION) {
	controllMenu.value = controllMenu.value.filter((item: any) => {
		return item.key !== '1'
	})
}
const backMenu = [
	{
		label: '返回',
		icon: back1,
		click: () => {
			if (!!componentName.value) {
				componentName.value = undefined
				menuKey.value = undefined
			} else {
				router.back()
			}
		},
	},
]

const onClose = () => {
	folderVisible.value = false
}
const onSuccess = () => {
	folderVisible.value = false
}

const onComponentClose = () => {
	componentName.value = undefined

	menuKey.value = undefined
}
// 获取user类型

const loadUserType = async () => {
	// 1-市管 2-区管 3-中层
	const res = await getUserType({ user_id })
	if (res.code === 0) {
		userType.value = res.data
		// 权限判断 ，市管没有干部画像和关系图谱
		if (AUTH_LIST.includes(route.path) && res.data == '1') {
			// 跳到审批表
			router.replace({
				path: '/cadre-portrait/cadre-table',
				query: {
					user_id,
				},
			})
		}
	}
}

loadUserType()
/**
 * @description: 初始化相关的方法在里面调用
 * @return {*}
 */
const onLoad = () => {
	initLeaderBaseInfoData()
}

provide('user_id', user_id)
provide('openRuleModal', onRuleModal)

watch(
	route,
	() => {
		const { path } = route
		const menuItem = _menu.find((item: any) => {
			return item.path === path || path.startsWith(item.path)
		})
		currentKey.value = menuItem?.key
		// 每次切换路由都要清楚弹出菜单
		componentName.value = undefined

		menuKey.value = undefined
	},
	{
		immediate: true,
	}
)
onLoad()

let load1: any = new Image()
let load2: any = new Image()
let load3: any = new Image()

load1.src = quguanImage
load3.src = zhongcengImage
load2.src = dangerImage
</script>

<style lang="less" scoped>
.cadre-portrait {
	display: flex;
	// flex-direction: column;
	width: 100%;
	height: 100%;
	position: relative;
	// .menu-box {
	// 	height: 84px;
	// 	width: 100%;
	// 	overflow: hidden;
	// 	.menu-list {
	// 		height: 100%;
	// 		width: 100%;
	// 		display: flex;
	// 		.menu-item {
	// 			padding: 0px 32px;
	// 			flex: 1;
	// 			height: 100%;
	// 			display: flex;
	// 			align-items: center;
	// 			justify-content: center;
	// 			font-size: 48px;
	// 			font-family: YouSheBiaoTiHei;
	// 			font-weight: 500;
	// 			color: #ffffff;
	// 			line-height: 30px;
	// 			cursor: pointer;
	// 			user-select: none;
	// 			background-color: #d23122;
	// 		}

	// 		.menu-active {
	// 			// background: url('@/assets/images/menu-active-bg.png') no-repeat center / 100% 100%;
	// 			// color: #00fbfe;
	// 			// border-color: #00fbfe;
	// 			color: #ffffff;
	// 			font-size: 52px;
	// 			background-image: linear-gradient(to bottom, #861207, #c72415);
	// 			justify-content: space-between;
	// 			// background: linear-gradient(0deg, rgba(17, 208, 201, 0.2) 0%, rgba(17, 208, 201, 0.1) 10%, transparent 30%),
	// 			// 	linear-gradient(90deg, rgba(17, 208, 201, 0.2) 0%, rgba(17, 208, 201, 0.1) 10%, transparent 20%),
	// 			// 	linear-gradient(180deg, rgba(17, 208, 201, 0.2) 0%, rgba(17, 208, 201, 0.1) 10%, transparent 20%),
	// 			// 	linear-gradient(270deg, rgba(17, 208, 201, 0.2) 0%, rgba(17, 208, 201, 0.1) 10%, transparent 30%);
	// 			&::before {
	// 				content: '';
	// 				display: inline-block;
	// 				width: 11.33px;
	// 				height: 16px;
	// 				background: url('@/assets/images/caret-left.png') no-repeat center / 100% 100%;
	// 				transform: rotateZ(180deg);
	// 			}
	// 			&::after {
	// 				content: '';
	// 				display: inline-block;
	// 				width: 11.33px;
	// 				height: 16px;
	// 				background: url('@/assets/images/caret-left.png') no-repeat center / 100% 100%;
	// 			}
	// 		}
	// 	}
	// }
	.menu-box {
		margin: 0px 16px;
		padding: 16px 0px;
		height: 100%;
		overflow: hidden;
		.menu-list {
			height: 100%;
			width: 100%;
			display: flex;
			flex-direction: column;
			.menu-item {
				padding: 24px 0px;
				display: flex;
				align-items: center;
				justify-content: center;
				writing-mode: vertical-lr;
				white-space: nowrap;
				width: 78px;
				height: 196px;
				border-radius: 8px;
				font-size: 26px;
				font-family: PingFang SC-Medium, PingFang SC;
				font-weight: 500;
				color: #222222;
				line-height: 30px;
				cursor: pointer;
				user-select: none;
				background-color: #ffffff;
			}
			.menu-item-1,
			.menu-item-3,
			.menu-item-4 {
				padding: 14px 0px;
			}
			.menu-active {
				// background: url('@/assets/images/menu-active-bg.png') no-repeat center / 100% 100%;
				// color: #00fbfe;
				// border-color: #00fbfe;
				color: #ffffff;
				background-color: #bb0c0c;
				// background: linear-gradient(0deg, rgba(17, 208, 201, 0.2) 0%, rgba(17, 208, 201, 0.1) 10%, transparent 30%),
				// 	linear-gradient(90deg, rgba(17, 208, 201, 0.2) 0%, rgba(17, 208, 201, 0.1) 10%, transparent 20%),
				// 	linear-gradient(180deg, rgba(17, 208, 201, 0.2) 0%, rgba(17, 208, 201, 0.1) 10%, transparent 20%),
				// 	linear-gradient(270deg, rgba(17, 208, 201, 0.2) 0%, rgba(17, 208, 201, 0.1) 10%, transparent 30%);
			}
		}
	}

	.user-box {
		width: 508px;
		height: 100%;
		overflow: hidden;
		padding: 11px 12px;
		background-color: #ffffff;
		border-radius: 8px;
		.user-info {
			padding: 12px 8px;
			width: 100%;
			height: 100%;
			background-color: #ecf5ff;
			border-radius: 8px;
			.info-box {
				height: 100%;
				display: flex;
				flex-direction: column;
			}
			.user-base__info {
				display: flex;
				flex-direction: column;
				.avatar {
					flex-shrink: 0;
					margin-right: 18px;
					width: 100px;
					height: 134px;
					background: url('@/assets/images/avatar.png') no-repeat center center / contain;
					// background-color: rgba(204, 204, 204, 0.2);
				}
				.top-info {
					display: flex;
				}
				.bottom-info,
				.info {
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					.user-name {
						font-size: 28px;
						font-family: PingFang SC-Bold, PingFang SC;
						font-weight: bold;
						color: #000000;
						line-height: 33px;
					}
					.info-item {
						display: flex;
						font-size: 20px;
						font-family: PingFang SC-Medium, PingFang SC;
						font-weight: 500;
						color: #333333;
						line-height: 38px;
						letter-spacing: 1px;
						.label {
							flex-shrink: 0;
							margin-right: 10px;
							span[type='label'] {
								display: inline-block;
								min-width: 85px;
								text-align-last: justify;
							}
						}

						.info {
						}
					}
				}
				.bottom-info {
					.info-item {
						.label {
							flex-shrink: 0;
							margin-right: 10px;
							span[type='label'] {
								display: inline-block;
								min-width: 203px;
								text-align-last: justify;
							}
						}
					}
				}
			}
			.label-container {
				flex: 1;
				overflow-y: auto;
				&::-webkit-scrollbar {
					display: none;
				}
			}
			.number-card {
				margin-top: 36px;
				display: flex;
				justify-content: space-between;
				.card-1,
				.card-2 {
					display: flex;
					align-items: center;
					justify-content: center;
					width: 252px;
					height: 45px;
					// background: url('@/assets/images/card1-bg.png') no-repeat center / cover;

					.label-icon {
						margin-right: 18px;
						display: inline-block;
						width: 16px;
						height: 16px;
						line-height: 1;
						// background: url('@/assets/images/icon-1.png') no-repeat center / 100% 100%;
						cursor: pointer;
					}
					.label {
						margin-right: 7px;
						font-size: 16px;
						font-weight: 400;
						// color: #96e9ff;
						line-height: 16px;
					}
					.label-1 {
						font-size: 20px;
						font-weight: bold;
						color: #f6e81f;
						line-height: 20px;
					}
					.label-2 {
						font-size: 15px;
						font-weight: bold;
						color: #f6e81f;
						// line-height: 15px;
					}
				}
				.card-1 {
					.label-1 {
						font-size: 20px;
						font-weight: bold;
						color: #f6e81f;
						line-height: 15px;
					}
					.label-2 {
						position: relative;
						font-weight: bold;
						color: #f6e81f;
						line-height: 15px;
						&::after {
							position: absolute;
							bottom: -5px;
							left: 16px;
							content: '';
							display: inline-block;
							width: 6px;
							height: 2px;
							background: #05b0ff;
						}
					}
				}
				.card-2 {
					.label-1 {
						font-size: 19px;
						font-weight: bold;
						color: #ff3b1b;
						line-height: 15px;
					}
				}
			}
			.label-info-box {
				.top-content {
					padding: 4px 12px 4px 18px;
					display: flex;
					justify-content: space-between;
					background: url('@/assets/images/card-header-bg1.png') no-repeat left center / 100% 100%;

					.more {
						display: flex;
						align-items: center;
						span {
							font-size: 18px;
							font-family: PingFang SC-Medium, PingFang SC;
							font-weight: 500;
							color: #666666;
							line-height: 18px;
						}
						&::after {
							content: '';
							display: inline-block;
							width: 24px;
							height: 24px;
							background: url('@/assets/images/chevron-right.png') no-repeat center / 100% 100%;
						}
					}
				}
				.label-info {
					.label {
						display: flex;
						align-items: center;
						font-size: 20px;
						font-family: PingFang SC-Bold, PingFang SC;
						font-weight: bold;
						color: #222222;
						line-height: 24px;
					}
					.content {
						display: flex;
						.text {
							padding: 14px 11px 20px 18px;
							font-size: 20px;
							font-family: PingFang SC-Medium, PingFang SC;
							font-weight: 500;
							color: #333333;
							line-height: 31px;
						}
					}
				}
			}
		}
	}
	.content-box {
		position: relative;
		margin: 0 104px 0px 0px;
		flex: 1;
		height: 100%;
		overflow: hidden;
	}
	.container {
		position: absolute;
		top: 0;
		left: 0;
		right: 104px;
		bottom: 0;
		padding: 16px 16px 12px;
		z-index: 9999;
		background: #f6f8fc;
	}
}

.modal-card {
	width: 900px;
	height: 750px;
	.modal-card-box {
		padding: 20px;
	}
	.modal-title {
		font-size: 20px;
		font-weight: bold;
		color: #000000;
		text-align: center;
	}
	.modal-content {
		padding: 4px 0;
		font-size: 16px;
		color: #ffffff;
		.content-item {
			padding: 10px 0;
			margin-top: 20px;
			border-bottom: 1px solid rgba(0, 234, 255, 0.1);
			&:nth-last-child(1) {
				border-bottom: none;
			}
			.text {
				font-size: 16px;
				font-weight: 400;
				color: #000000;
				line-height: 25px;
			}
			.user-name {
				line-height: 20px;
				margin: 12px 20px 0 0;
				// float: right;
				text-align: right;
				font-size: 14px;
				font-weight: 500;
				color: #000000;
				line-height: 14px;
			}
		}
	}
}
.tag-modal {
	.content-item {
		.tag-box {
			display: flex;
			flex-direction: column;
			flex-wrap: wrap;
			.tag-item {
				margin-bottom: 10px;
			}
		}
		.text {
			font-size: 16px;
			font-weight: 400;
			color: #00eaff;
			line-height: 25px;
		}
	}
}
.tag-item {
	margin-right: 29px;
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 0 18px;
	min-width: 131px;
	height: 36px;

	font-size: 18px;
	font-weight: 500;
	color: #00eaff;
	line-height: 14px;

	border: 1px solid #00eaff;
	border-radius: 5px;

	background: linear-gradient(0deg, rgba(17, 208, 201, 0.2) 0%, rgba(17, 208, 201, 0.1) 10%, transparent 30%),
		linear-gradient(90deg, rgba(17, 208, 201, 0.2) 0%, rgba(17, 208, 201, 0.1) 10%, transparent 20%),
		linear-gradient(180deg, rgba(17, 208, 201, 0.2) 0%, rgba(17, 208, 201, 0.1) 10%, transparent 20%),
		linear-gradient(270deg, rgba(17, 208, 201, 0.2) 0%, rgba(17, 208, 201, 0.1) 10%, transparent 30%);
}
.a-pointer {
	cursor: pointer;
}
.margintop-47 {
	margin-top: 24px !important;
}
//
.container-enter-active,
.container-leave-active {
	transition: all 0.2s ease-out;
}
.container-enter,
.container-leave-to {
	opacity: 0;
}
</style>

<style lang="scss">
.omit-text {
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 3; /* 限制显示两行 */
	overflow: hidden;
	text-overflow: ellipsis; /* 显示省略号 */
	white-space: normal;
}

.folder-modal {
	width: 752px;
	.ant-modal-body {
		padding: 0px !important;
	}
}
</style>
