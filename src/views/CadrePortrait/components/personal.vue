<template>
	<div class="personal">
		<div class="title">{{ user.name }}</div>
		<div class="radio-box">
			<div :class="`radio ${item.key === activeKey ? 'active' : ''}`" @click="onActive(item.key)" v-for="(item, index) in radio" :key="item.key">
				{{ item.label }}
			</div>
		</div>
		<div class="table-box">
			<div v-if="activeKey === '1'">
				<a-table :columns="columns1" bordered :data-source="state1" :pagination="false" />
			</div>
			<div v-if="activeKey === '3'">
				<!-- 履职负面清单 -->
				<a-table :columns="columns3" bordered :data-source="state3" :pagination="false" />
			</div>
			<div v-if="activeKey === '4'">
				<!-- 重大事项报告 -->
				<a-table :columns="columns4" bordered :data-source="state4" :pagination="false" />
			</div>
			<div v-if="activeKey === '5'" class="matters">
				<div class="container">
					<div class="content">
						<div class="card">
							<div class="info-title">婚姻状况</div>
							<div class="table">
								<a-table :columns="columnsMatters1" bordered :data-source="state.marriages" :pagination="false" />
							</div>
						</div>
						<div class="card m-top-72">
							<div class="info-title">出国（境）证件记录</div>
							<div class="table">
								<a-table :columns="columnsMatters2" bordered :data-source="state.certs" :pagination="false" />
							</div>
						</div>
						<div class="card m-top-72">
							<div class="info-title">兼职情况</div>
							<div class="table">
								<a-table :columns="columnsPartTime" bordered :data-source="partTime" :pagination="false" />
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script lang="ts" setup>
import { complaintReport, getPartTimeJob, getPeronelMatter, getSupervision, mattersCheck } from '@/apis/cadre-portrait/home'
import { message } from 'ant-design-vue'
import { onMounted, ref } from 'vue'
import { formatDate } from '@/utils/utils'
//#region ====================== 定义变量 ======================
const props = defineProps({
	user: {
		type: Object,
		default: () => ({}),
		required: true,
	},
})
const radio = [
	{
		label: '信访举报情况',
		key: '1',
	},
	{
		label: '履职负面清单',
		key: '3',
	},
	{
		label: '重大事项报告',
		key: '4',
	},
	{
		label: '其他监督',
		key: '5',
	},
]

const columns1 = [
	{
		dataIndex: 'report_date',
		key: 'report_date',
		title: '信访日期',
		width: '10%',
		align: 'center',
	},
	{
		dataIndex: 'register_org',
		key: 'register_org',
		title: '登记机构',
		width: '10%',
		align: 'center',
	},
	{
		dataIndex: 'register_reason',
		key: 'register_reason',
		title: '信访目的',
		width: '10%',
		align: 'center',
	},
	{
		dataIndex: 'register_form',
		key: 'register_form',
		title: '信访形式',
		width: '10%',
		align: 'center',
	},
	{
		dataIndex: 'register_count',
		key: 'register_count',
		title: '信访人数',
		width: '10%',
		align: 'center',
	},
	{
		dataIndex: 'jointly',
		key: 'jointly',
		title: '是否联名',
		width: '10%',
		align: 'center',
	},
	{
		dataIndex: 'content',
		key: 'content',
		title: '概况信息',
		width: '30%',
		align: 'center',
	},
]

const columns3 = [
	{
		title: '时间',
		dataIndex: 'date',
		key: 'date',
		align: 'center',
	},
	{
		title: '内容',
		dataIndex: 'content',
		key: 'content',
		align: 'center',
	},
]

const columns4 = [
	{
		title: '时间',
		dataIndex: 'date',
		key: 'date',
		align: 'center',
	},
	{
		title: '内容',
		dataIndex: 'content',
		key: 'content',
		align: 'center',
	},
]

const columnsMatters1 = [
	{
		dataIndex: 'native_place',
		key: 'native_place',
		title: '籍贯',
		align: 'center',
		width: '20%',
	},
	{
		dataIndex: 'position',
		key: 'position',
		title: '现任职务',
		align: 'center',
		width: '20%',
	},
	{
		dataIndex: 'birthday',
		key: 'birthday',
		title: '出生年月',
		align: 'center',
		width: '20%',
	},
	{
		dataIndex: 'content',
		key: 'content',
		title: '婚姻状况',
		align: 'center',
		width: '20%',
	},
]

const columnsMatters2 = [
	{
		dataIndex: 'cert_name',
		key: 'cert_name',
		title: '证件名称',
		align: 'center',
		width: '20%',
	},
	{
		dataIndex: 'cert_number',
		key: 'cert_number',
		title: '证件号码',
		align: 'center',
		width: '20%',
	},
	{
		dataIndex: 'cert_validity',
		key: 'cert_validity',
		title: '证件有效期',
		align: 'center',
		width: '20%',
	},
	{
		dataIndex: 'situation',
		key: 'situation',
		title: '使用情况',
		align: 'center',
		width: '20%',
	},
]

const columnsPartTime = [
	{
		title: '兼任职务',
		dataIndex: 'part_time_position',
		key: 'part_time_position',
		align: 'center',
	},
	{
		title: '所兼职社会团体(企业)业务主管部门',
		dataIndex: 'business_department',
		key: 'business_department',
		align: 'center',
	},
	{
		title: '兼职时间',
		dataIndex: 'part_time',
		key: 'part_time',
		align: 'center',
	},
	{
		title: '任期届数',
		dataIndex: 'term_count',
		key: 'term_count',
		align: 'center',
	},
	{
		title: '是否兼任法人代表',
		dataIndex: 'is_legal_representative',
		key: 'is_legal_representative',
		align: 'center',
	},
	{
		title: '是否报批',
		dataIndex: 'is_approved',
		key: 'is_approved',
		align: 'center',
	},
	{
		title: '审批文号',
		dataIndex: 'approval_number',
		key: 'approval_number',
		align: 'center',
	},
]
const state = ref({
	marriages: [],
	certs: [],
})
const state1 = ref([])
const state3 = ref([])
const state4 = ref([])
const partTime = ref([]) //兼职情况
const activeKey = ref(radio[0].key)
//#endregion

//#region ====================== 初始化数据源 ======================
const onActive = (key: any) => {
	activeKey.value = key
}
const fetchData = async (api: any, stateRef: any, user_id: string) => {
	const res = await api(user_id)
	if (res.code === 0) {
		stateRef.value = res.data
	}
}

const initPartTimeJob = async (user_id: string) => {
	try {
		const res = await getPartTimeJob(user_id)
		if (res.code === 0) {
			if (res.data) {
				// 处理每个兼职记录，合并开始时间和结束时间
				partTime.value = res.data?.data?.map((item: any) => ({
					...item,
					part_time_position: item.part_time_position,
					business_department: item.business_department,
					part_time: `${formatDate(item.part_start_time)} - ${formatDate(item.part_end_time)}`,
					term_count: item.term_count,
					is_legal_representative: item.is_legal_representative === 1 ? '是' : item.is_legal_representative === 0 ? '否' : '未知',
					is_approved: item.is_approved === 1 ? '是' : item.is_approved === 0 ? '否' : '未知',
					approval_number: item.approval_number,
				}))
			} else {
				partTime.value = []
			}
		} else {
			message.error(res.message)
		}
	} catch (error) {
		message.error('获取兼职情况失败')
	}
}
//#endregion
onMounted(() => {
	const { user_id } = props.user
	fetchData(complaintReport, state1, user_id)
	fetchData(getPeronelMatter, state, user_id)
	fetchData(mattersCheck, state3, user_id)
	fetchData(getSupervision, state4, user_id)
	initPartTimeJob(user_id)
})
</script>

<style lang="less" scoped>
.personal {
	padding: 36px 50px 36px 70px;
	width: 100%;
	height: 100%;
	background: #ffffff;
	overflow: auto;

	.title {
		font-size: 28px;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 600;
		color: #000000;
		line-height: 33px;
	}
	.radio-box {
		margin-top: 38px;
		display: flex;
		.radio {
			border: 1px solid rgba(229, 37, 27, 0.4);
			margin-right: 34px;
			padding: 8px 10px;
			background: rgba(229, 37, 27, 0.03);
			border-radius: 4px;
			font-size: 20px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			color: #e5251b;
			line-height: 23px;
		}
		.active {
			background: #e5251b;
			color: #ffffff;
		}
	}
	.table-box {
		margin-top: 24px;
		.matters {
			.container {
				padding: 16px 0px;
				flex: 1;
				width: 100%;
				.content {
					padding: 0px 0px;
					width: 100%;
					height: 100%;
					overflow-y: auto;
					background-color: #ffffff;
					.card {
						.info-title {
							display: flex;
							align-items: center;
							font-size: 22px;
							font-family: Source Han Sans CN-Medium, Source Han Sans CN;
							font-weight: 500;
							color: #000000;
							line-height: 22px;
							&::before {
								margin-right: 10px;
								content: '';
								display: inline-block;
								width: 12px;
								height: 12px;
								background-color: #ec4224;
								border-radius: 50%;
							}
						}
						.table {
							margin-top: 17px;
						}
					}
				}
			}
		}
	}
}

:deep(.ant-table-container) {
	.ant-table-cell {
		font-size: 20px !important;
	}
	.ant-table-tbody {
		.ant-table-cell {
			font-size: 18px !important;
		}
	}
}
</style>
